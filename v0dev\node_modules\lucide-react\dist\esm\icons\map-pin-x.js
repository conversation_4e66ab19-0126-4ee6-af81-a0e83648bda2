/**
 * @license lucide-react v0.534.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  [
    "path",
    {
      d: "M19.752 11.901A7.78 7.78 0 0 0 20 10a8 8 0 0 0-16 0c0 4.993 5.539 10.193 7.399 11.799a1 1 0 0 0 1.202 0 19 19 0 0 0 .09-.077",
      key: "y0ewhp"
    }
  ],
  ["circle", { cx: "12", cy: "10", r: "3", key: "ilqhr7" }],
  ["path", { d: "m21.5 15.5-5 5", key: "11iqnx" }],
  ["path", { d: "m21.5 20.5-5-5", key: "1bylgx" }]
];
const MapPinX = createLucideIcon("map-pin-x", __iconNode);

export { __iconNode, MapPinX as default };
//# sourceMappingURL=map-pin-x.js.map
