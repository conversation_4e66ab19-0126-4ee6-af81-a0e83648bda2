/**
 * @license lucide-react v0.534.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  [
    "path",
    {
      d: "M15 22a1 1 0 0 1-1-1v-4a1 1 0 0 1 .445-.832l3-2a1 1 0 0 1 1.11 0l3 2A1 1 0 0 1 22 17v4a1 1 0 0 1-1 1z",
      key: "1p1rcz"
    }
  ],
  [
    "path",
    {
      d: "M18 10a8 8 0 0 0-16 0c0 4.993 5.539 10.193 7.399 11.799a1 1 0 0 0 .601.2",
      key: "mcbcs9"
    }
  ],
  ["path", { d: "M18 22v-3", key: "1t1ugv" }],
  ["circle", { cx: "10", cy: "10", r: "3", key: "1ns7v1" }]
];
const MapPinHouse = createLucideIcon("map-pin-house", __iconNode);

export { __iconNode, MapPinHouse as default };
//# sourceMappingURL=map-pin-house.js.map
